using System;
using UnityEngine;
using UnityEngine.UI;
using QFramework;

namespace QFramework.ZSS
{
	// Generate Id:2497f856-e5f9-4fc6-b042-cd89eb8ec775
	public partial class AheadStudyPanel
	{
		public const string Name = "AheadStudyPanel";
		
		[SerializeField]
		public UnityEngine.UI.Button Btn_Close;
		[SerializeField]
		public UnityEngine.UI.Button Btn_Ok;
		
		private AheadStudyPanelData mPrivateData = null;
		
		protected override void ClearUIComponents()
		{
			Btn_Close = null;
			Btn_Ok = null;
			
			mData = null;
		}
		
		public AheadStudyPanelData Data
		{
			get
			{
				return mData;
			}
		}
		
		AheadStudyPanelData mData
		{
			get
			{
				return mPrivateData ?? (mPrivateData = new AheadStudyPanelData());
			}
			set
			{
				mUIData = value;
				mPrivateData = value;
			}
		}
	}
}
