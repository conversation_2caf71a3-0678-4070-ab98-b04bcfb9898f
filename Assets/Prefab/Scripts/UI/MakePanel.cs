using UnityEngine;
using UnityEngine.UI;
using QFramework;
using System;
namespace QFramework.ZSS
{
   
    public class MakePanelData : UIPanelData
	{
	}
	public partial class MakePanel : UIPanel,IController
	{
        // 存储需要切换的图片（Sprite 数组）
        public Sprite[] ShowImages; // Show 对应的图片
        public Sprite[] HideImages; // Hide 对应的图片

        // 存储各个按钮的状态
        public Button[] sortButtons; // Sort 按钮
        public Button[] longButtons; // Long 按钮

       
        protected override void OnInit(IUIData uiData = null)
		{
			mData = uiData as MakePanelData ?? new MakePanelData();
            // please add init code here
            Btn_Find.onClick.AddListener(() =>
            {
               
                LookupSeat();
                GetArchitecture().SendEvent(new ClickEnableEvent(true));
                Debug.Log("查找座位");
            });

            Btn_Ranking.onClick.AddListener(() =>
            {
               
                Debug.Log("查看排名");
                StartManager.Instance.StartMyCorutine(this.GetSystem<HttpsSystem>().GetDurationRankinglistt("week"));
            });

            Btn_Cut.onClick.AddListener(() =>
            {

                SyncUIKit.OpenPanel<ChangeNomarlUI>(UILevel.PopUI);
            });

            Btn_Help.onClick.AddListener(() =>
            {

                SyncUIKit.OpenPanel<NotificationPanel>(UILevel.PopUI);
            });

            //排序
            Btn_Sort_Icon.onClick.AddListener(() => {

                Debug.Log("排序图标");
                if (Img_Sort.gameObject.activeSelf)
                {
                    Img_Sort.Hide();
                    Btn_Sort_Icon.gameObject.transform.localEulerAngles = new Vector3(0, 0, 0);
                }
                else
                {
                    Img_Sort.Show();
                    //排序图标 点击后旋转180度
                    Btn_Sort_Icon.gameObject.transform.localEulerAngles = new Vector3(0, 0, 180);
                }

            });
            Btn_Long_Icon.onClick.AddListener(() =>
            {
                if (Img_Long.gameObject.activeSelf)
                {
                    Img_Long.Hide();
                    Btn_Long_Icon.gameObject.transform.localEulerAngles = new Vector3(0, 0, 0);
                }
                else
                {
                    Img_Long.Show();
                    Btn_Long_Icon.gameObject.transform.localEulerAngles = new Vector3(0, 0, 180);
                }

            });

            Btn_Arrow.onClick.AddListener(() =>
            {
                Debug.Log("箭头");
                //获取当前位置 预约时间
            });

            //排序选择
            Btn_Sort_1.onClick.AddListener(() => {

                OnButtonClicked(0, true );
            });
            Btn_Sort_2.onClick.AddListener(() => {
                OnButtonClicked( 1, true);
            });
            Btn_Sort_3.onClick.AddListener(() => {
                OnButtonClicked( 2, false);
            });
            Btn_Sort_4.onClick.AddListener(() => {
                OnButtonClicked(3, true);
            });
            Btn_Long_1.onClick.AddListener(() => {
                OnButtonClicked(0, true);
            });
            Btn_Long_2.onClick.AddListener(() => {
                OnButtonClicked(1, true);
            });
            Btn_Long_3.onClick.AddListener(() => {
                OnButtonClicked(2, false);
            });
            Btn_Long_4.onClick.AddListener(() => {

                OnButtonClicked(3, true);
            });
            Btn_Arrow.onClick.AddListener(() =>
            {  
                //获取当前位置 预约时间
                StartManager.Instance.StartMyCorutine(this.GetSystem<HttpsSystem>().GetSeatBookingTime(DateTime.Now.ToString("yyyy-MM-dd"), this.GetSystem<ScenceSystem>().GetCurrentSceneIndex().ToString(), this.GetSystem<SubscribeSystem>().CurrectSeatID));
             
            });
         
            //预约时间信息
            this.RegisterEvent<OnSeatBookEvent>(e =>
            {
                Debug.Log(e.timeslotInfos.Count);
                this.GetSystem<SubscribeSystem>().TimeSlotPool = e.timeslotInfos;
                //打开预约面板
                SyncUIKit.OpenPanel<SubscribePanel>(UILevel.PopUI);

            }).UnRegisterWhenGameObjectDestroyed(gameObject);
        }
		
		protected override void OnOpen(IUIData uiData = null)
		{

        
        }
		
		protected override void OnShow()
		{
		}
		
		protected override void OnHide()
		{
		}
		
		protected override void OnClose()
		{
		}

        // 通过按钮点击事件记录当前选择
        private void OnButtonClicked( int index, bool isSort)
        {  
            //所有按钮图片全部还原成灰色
            for (int i = 0; i < 4; i++)
            {
                sortButtons[i].GetComponent<Image>().sprite= HideImages[i];
                longButtons[i].GetComponent<Image>().sprite = HideImages[i];
            }
            //选中的按钮图片也改变成一个
            Btn_Sort_Image.sprite = ShowImages[index];
            Btn_Long_Image.sprite = ShowImages[index];
            //排序图 关闭
            if (Img_Sort.gameObject.activeSelf)
            {
                Img_Sort.Hide();
                Btn_Sort_Icon.gameObject.transform.localEulerAngles = new Vector3(0, 0, 0);
            }
            if (Img_Long.gameObject.activeSelf)
            {
                Img_Long.Hide();
                Btn_Long_Icon.gameObject.transform.localEulerAngles = new Vector3(0, 0, 0);
            }
            if (!isSort)
            {
                if (Sort.gameObject.activeSelf)
                {
                    Sort.Hide();
                    Long.Show();
                }
                else
                {
                    Sort.Show();
                    Long.Hide();
                }

            }
            else
            {
                if (Long.gameObject.activeSelf)
                {
                    Sort.Show();
                    Long.Hide();
                }
               
            }
            //选择场景时，切换图片并判断当前选择的场景是否一致
            if (this.GetSystem<ScenceSystem>().CurrentScene!= this.GetSystem<ScenceSystem>().GetImageScenceIndex(index))
            {
                PopPanelManager.ShowTips("是否切换场景",()=>{

                    PopPanelManager.CloseTips();


                }, () => {

                    this.GetModel<UserModel>().IsOneOpenScence = false;
                    this.GetSystem<ScenceSystem>().LastScene = this.GetSystem<ScenceSystem>().CurrentScene;
                    this.GetSystem<ScenceSystem>().CurrentScene = this.GetSystem<ScenceSystem>().GetImageScenceIndex(index);
                    this.SendCommand(new SceneChangeCommand(index));
                    PopPanelManager.CloseTips();
                });
              
            }

        }

        public void ChangeImgae(int index, bool isSort)
        {
            //所有按钮图片全部还原成灰色
            for (int i = 0; i < 4; i++)
            {
                sortButtons[i].GetComponent<Image>().sprite = HideImages[i];
                longButtons[i].GetComponent<Image>().sprite = HideImages[i];
            }
            //选中的按钮图片也改变成一个
            Btn_Sort_Image.sprite = ShowImages[index];
            Btn_Long_Image.sprite = ShowImages[index];
            //排序图 关闭
            if (Img_Sort.gameObject.activeSelf)
            {
                Img_Sort.Hide();
                Btn_Sort_Icon.gameObject.transform.localEulerAngles = new Vector3(0, 0, 0);
            }
            if (Img_Long.gameObject.activeSelf)
            {
                Img_Long.Hide();
                Btn_Long_Icon.gameObject.transform.localEulerAngles = new Vector3(0, 0, 0);
            }
            if (!isSort)
            {
                if (Sort.gameObject.activeSelf)
                {
                    Sort.Hide();
                    Long.Show();
                }
                else
                {
                    Sort.Show();
                    Long.Hide();
                }

            }
            else
            {
                if (Long.gameObject.activeSelf)
                {
                    Sort.Show();
                    Long.Hide();
                }

            }

        }

   

        /// <summary>
        /// 查找空座
        /// </summary>
        private void LookupSeat()
        {
            Debug.Log("[MakePanel] 手动找空座按钮被点击");
            // 获取座位信息
            // 查询空闲座位
            // 获取当前场景的空闲座位ID为Select_Seat
            // 移动镜头指向该座位
            // 点击Btn_Arrow
            var index = this.GetSystem<SubscribeSystem>().GetSeatID();
            Debug.Log($"[MakePanel] 获取到座位ID: {index}");

            if (index.Equals("没有空位"))
            {
                //TODO: 没有空座位提示
                Debug.Log("[MakePanel] 没有空座位");
                PopPanelManager.ShowTips("没有空座位");
                return;
            }
            var go = this.GetSystem<SeatGameObjSystem>().GetSeatGameObject(this.GetSystem<ScenceSystem>().CurrentScene,index).transform;
            Debug.Log("[MakePanel] 执行相机移动命令");
            this.SendCommand(new CamerMoveCommand(go));
            this.GetSystem<SubscribeSystem>().CurrectSeatID = int.Parse(index);
            Debug.Log($"[MakePanel] 设置当前座位ID: {index}");
            this.SendCommand(new ArrowMoveCommand(this.GetSystem<SeatGameObjSystem>().FindSeat(this.GetSystem<ScenceSystem>().CurrentScene, SeatType.A, index).transform, index));
            Debug.Log("[MakePanel] 手动找空座功能执行完成");

  
        }

        public IArchitecture GetArchitecture()
        {
            return LibraryApp.Interface;
        }
    }
  
    
}

