using System.Collections;
using UnityEngine;
using UnityEngine.UI;
using QFramework;

namespace QFramework.ZSS
{
	public class MainPanelData : UIPanelData
	{
	}

	// 面板类型枚举
	public enum PanelType { Sign, OneSelf, Make }

	public partial class MainPanel : UIPanel, IController
	{
		// 定义一个布尔类型的 "状态位"
		// 用来记录当前是否正在获取用户信息
		private bool mIsLoadingUserData = false;
		
		// 添加防抖机制
		private bool mIsSwitching = false;
		private float mLastSwitchTime = 0f;
		private const float SWITCH_COOLDOWN = 0.2f; // 减少到200ms
		private const float MAX_SWITCH_TIME = 2f; // 最大切换时间，超过则强制重置
		private Coroutine mCurrentSwitchCoroutine = null;

		protected override void OnInit(IUIData uiData = null)
		{
			mData = uiData as MainPanelData ?? new MainPanelData();
			// please add init code here

			Toggle_Make.onValueChanged.AddListener(e => {
			    if (e) SwitchToPanel(PanelType.Make);
			    else SyncUIKit.ClosePanel<MakePanel>();
			});

			// 使用统一的面板切换方法
			Toggle_Sign.onValueChanged.AddListener(e => {
			    if (e) SwitchToPanel(PanelType.Sign);
			    else SyncUIKit.ClosePanel<SignPanel>();
			});

			Toggle_OneSelf.onValueChanged.AddListener(e => {
			    if (e) SwitchToPanel(PanelType.OneSelf);
			    else SyncUIKit.ClosePanel<OneSelfPanel>();
			});
		}

		/// <summary>
		/// 统一的面板切换方法，带防抖机制和状态重置
		/// </summary>
		private void SwitchToPanel(PanelType targetPanel)
		{
			float currentTime = Time.time;
			
			// 检查是否需要强制重置状态
			if (mIsSwitching && (currentTime - mLastSwitchTime) > MAX_SWITCH_TIME)
			{
				Debug.LogWarning("面板切换状态超时，强制重置");
				ForceResetSwitchState();
			}
			
			// 防抖检查
			if (mIsSwitching || (currentTime - mLastSwitchTime) < SWITCH_COOLDOWN)
			{
				Debug.Log("面板切换过于频繁，请稍后再试");
				return;
			}
			
			mLastSwitchTime = currentTime;
			
			// 停止之前的切换协程
			if (mCurrentSwitchCoroutine != null)
			{
				StopCoroutine(mCurrentSwitchCoroutine);
				mCurrentSwitchCoroutine = null;
			}
			
			mCurrentSwitchCoroutine = StartCoroutine(SafeSwitchPanel(targetPanel));
		}

		/// <summary>
		/// 强制重置切换状态
		/// </summary>
		private void ForceResetSwitchState()
		{
			mIsSwitching = false;
			if (mCurrentSwitchCoroutine != null)
			{
				StopCoroutine(mCurrentSwitchCoroutine);
				mCurrentSwitchCoroutine = null;
			}
		}

		/// <summary>
		/// 安全的面板切换协程（移除try-catch以避免yield return错误）
		/// </summary>
		private IEnumerator SafeSwitchPanel(PanelType targetPanel)
		{
			mIsSwitching = true;
			
			// 先关闭所有面板
			CloseAllPanels();
			
			// 等待一帧确保面板完全关闭
			yield return null;
			
			// 重置所有Toggle状态
			ResetAllToggles(targetPanel);
			
			// 再等待一帧确保Toggle状态更新
			yield return null;
			
			// 打开目标面板
			switch (targetPanel)
			{
				case PanelType.Sign:
					SyncUIKit.OpenPanel<SignPanel>(UILevel.PopUI);
					SyncUIKit.SetPanelAsLast<SignPanel>();
					GetArchitecture().SendEvent(new ClickEnableEvent(true));
					break;
					
				case PanelType.OneSelf:
					if (!mIsLoadingUserData)
						StartCoroutine(GetUserDataAndOpenPanel());
					break;
					
				case PanelType.Make:
					SyncUIKit.OpenPanel<MakePanel>();
					break;
			}
			
			// 确保状态一定会被重置
			mIsSwitching = false;
			mCurrentSwitchCoroutine = null;
		}

		/// <summary>
		/// 关闭所有面板
		/// </summary>
		private void CloseAllPanels()
		{
			try
			{
				if (SyncUIKit.IsPanelOpen<SignPanel>())
					SyncUIKit.ClosePanel<SignPanel>();
				if (SyncUIKit.IsPanelOpen<OneSelfPanel>())
					SyncUIKit.ClosePanel<OneSelfPanel>();
				if (SyncUIKit.IsPanelOpen<MakePanel>())
					SyncUIKit.ClosePanel<MakePanel>();
			}
			catch (System.Exception e)
			{
				Debug.LogError($"关闭面板异常: {e.Message}");
			}
		}

		/// <summary>
		/// 重置所有Toggle状态
		/// </summary>
		private void ResetAllToggles(PanelType targetPanel)
		{
			try
			{
				// 临时移除所有监听器
				Toggle_Sign.onValueChanged.RemoveAllListeners();
				Toggle_OneSelf.onValueChanged.RemoveAllListeners();
				Toggle_Make.onValueChanged.RemoveAllListeners();
				
				// 重置状态
				Toggle_Sign.isOn = (targetPanel == PanelType.Sign);
				Toggle_OneSelf.isOn = (targetPanel == PanelType.OneSelf);
				Toggle_Make.isOn = (targetPanel == PanelType.Make);
				
				// 重新添加监听器
				Toggle_Sign.onValueChanged.AddListener(e => {
				    if (e) SwitchToPanel(PanelType.Sign);
				    else SyncUIKit.ClosePanel<SignPanel>();
				});
				Toggle_OneSelf.onValueChanged.AddListener(e => {
				    if (e) SwitchToPanel(PanelType.OneSelf);
				    else SyncUIKit.ClosePanel<OneSelfPanel>();
				});
				Toggle_Make.onValueChanged.AddListener(e => {
				    if (e) SwitchToPanel(PanelType.Make);
				    else SyncUIKit.ClosePanel<MakePanel>();
				});
			}
			catch (System.Exception e)
			{
				Debug.LogError($"重置Toggle状态异常: {e.Message}");
			}
		}

		/// <summary>
		/// 获取用户数据并打开OneSelf面板的协程
		/// </summary>
		private IEnumerator GetUserDataAndOpenPanel()
		{
		    mIsLoadingUserData = true;
		    
		    // 立即打开面板，使用PopUI层级确保在最上层
		    SyncUIKit.OpenPanel<OneSelfPanel>(UILevel.PopUI);
		    SyncUIKit.SetPanelAsLast<OneSelfPanel>(); // 确保在最上层显示
		    
		    // 异步获取用户数据
		    yield return this.GetSystem<HttpsSystem>().GetUserData();
		    
		    // 数据加载完成后刷新显示
		    if (SyncUIKit.IsPanelOpen<OneSelfPanel>())
		    {
		        var panel = SyncUIManager.Instance.GetPanel<OneSelfPanel>();
		        if (panel != null)
		        {
		            try
		            {
		                // 使用反射调用OnShow方法刷新数据
		                panel.GetType().GetMethod("OnShow", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)?.Invoke(panel, null);
		            }
		            catch (System.Exception e)
		            {
		                Debug.LogError($"刷新面板数据异常: {e.Message}");
		            }
		        }
		    }
		    
		    mIsLoadingUserData = false;
		}

		/// <summary>
		/// 提供给外部的状态重置方法（调试用）
		/// </summary>
		[ContextMenu("重置面板切换状态")]
		public void ResetPanelSwitchState()
		{
			ForceResetSwitchState();
			Debug.Log("面板切换状态已重置");
		}

		protected override void OnOpen(IUIData uiData = null)
		{
		}

		protected override void OnShow()
		{
		}

		protected override void OnHide()
		{
		}

		protected override void OnClose()
		{
			// 面板关闭时重置状态
			ForceResetSwitchState();
		}

		public IArchitecture GetArchitecture()
		{
			return LibraryApp.Interface;
		}
	}
}
