/****************************************************************************
 * Copyright (c) 2017 ~ 2020.1 liangxie
 * 
 * http://qframework.io
 * https://github.com/liangxiegame/QFramework
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 * 
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 * 
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
 * THE SOFTWARE.
 ****************************************************************************/

using System;
using UnityEngine;

namespace QFramework
{
    public class PanelSearchKeys : IPoolType, IPoolable
    {
        public Type PanelType;

        public string AssetBundleName;

        public string GameObjName;

        public UILevel Level = UILevel.Common;

        public IUIData UIData;
        
        
        public IPanel Panel;
        
        public PanelOpenType OpenType = PanelOpenType.Single;

        // 添加公共无参构造函数
        public  PanelSearchKeys()
        {
            // 可以在这里初始化默认值
            Level = UILevel.Common;
            OpenType = PanelOpenType.Single;
        }

        public void OnRecycled()
        {
            PanelType = null;
            AssetBundleName = null;
            GameObjName = null;
            UIData = null;
            Panel = null;
        }

        public bool IsRecycled { get; set; }


        public override string ToString()
        {
            return
                $"PanelSearchKeys PanelType:{PanelType} AssetBundleName:{AssetBundleName} GameObjName:{GameObjName} Level:{Level} UIData:{UIData}";
        }

        public static PanelSearchKeys Allocate()
        {
            try
            {
                return SafeObjectPool<PanelSearchKeys>.Instance.Allocate();
            }
            catch (System.Exception ex)
            {
                // 在WebGL环境下SafeObjectPool可能失败，使用直接创建作为备用方案
                UnityEngine.Debug.LogWarning($"SafeObjectPool分配PanelSearchKeys失败，使用直接创建方式: {ex.Message}");
                return new PanelSearchKeys();
            }
        }

        public void Recycle2Cache()
        {
            try
            {
                SafeObjectPool<PanelSearchKeys>.Instance.Recycle(this);
            }
            catch (System.Exception ex)
            {
                // 在WebGL环境下SafeObjectPool可能失败，忽略回收错误
                UnityEngine.Debug.LogWarning($"SafeObjectPool回收PanelSearchKeys失败，忽略回收: {ex.Message}");
                // 手动重置对象状态
                OnRecycled();
            }
        }
    }
}