using UnityEngine;

namespace QFramework
{
    public class FluentMusicAPI : IPoolable, IPoolType
    {
        // 添加公共无参构造函数
        public FluentMusicAPI()
        {
            // 初始化默认值
            mLoop = true;
            mVolumeScale = 1;
        }
        
        public static FluentMusicAPI Allocate()
        {
            try
            {
                return SafeObjectPool<FluentMusicAPI>.Instance.Allocate();
            }
            catch (System.Exception ex)
            {
                // WebGL环境下SafeObjectPool可能失败，直接创建新对象
                UnityEngine.Debug.LogWarning($"FluentMusicAPI SafeObjectPool.Allocate failed: {ex.Message}, creating new instance");
                return new FluentMusicAPI();
            }
        }

        public void OnRecycled()
        {
            mName = null;
            mClip = null;
            mLoop = true;
            mVolumeScale = 1;
        }

        public bool IsRecycled { get; set; }

        public void Recycle2Cache()
        {
            try
            {
                SafeObjectPool<FluentMusicAPI>.Instance.Recycle(this);
            }
            catch (System.Exception ex)
            {
                // WebGL环境下回收可能失败，忽略错误继续执行
                UnityEngine.Debug.LogWarning($"FluentMusicAPI SafeObjectPool.Recycle failed: {ex.Message}, ignoring recycle");
            }
        }

        private string mName = null;
        private AudioClip mClip = null;
        private bool mLoop = true;
        private float mVolumeScale = 1;

        public FluentMusicAPI WithName(string name)
        {
            mName = name;
            return this;
        }

        public FluentMusicAPI WithAudioClip(AudioClip clip)
        {
            mClip = clip;
            return this;
        }

        public FluentMusicAPI Loop(bool loop)
        {
            mLoop = loop;
            return this;
        }

        public FluentMusicAPI VolumeScale(float volumeScale)
        {
            mVolumeScale = volumeScale;
            return this;
        }
        
        public void Play()
        {
            if (mName != null)
            {
                AudioKit.PlayMusic(mName, mLoop, onEndCallback: Recycle2Cache, volume: mVolumeScale);
            }
            else
            {
                AudioKit.PlayMusic(mClip, mLoop, onEndCallback: Recycle2Cache, volume: mVolumeScale);
            }
        }
    }
}