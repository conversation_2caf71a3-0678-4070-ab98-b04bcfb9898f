using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace QFramework.ZSS
{
    public class MaskDataModel : AbstractModel
    {
        //预约状态（1、reserved 已预约 2、signed 已签到 3、unclaimed 逾期 4、cancelled 取消 5、signed_out 已签退 6、suspending 暂停中
        /// <summary>
        /// 预约记录数据
        /// </summary>
        private List<BookingData> mMaskDataList = new List<BookingData>() ;
        /// <summary>
        /// 0 全部     1、reserved 代签到      2、signed 已签到 （已经完成）
        /// </summary>
        public int GetMsakListType = 0;
        protected override void OnInit()
        {
            
        }

        /// <summary>
        /// 跟新预约记录数据
        /// </summary>
        /// <param name="data"></param>
        public void UpdateMaskData(List<BookingData> datas)
        {
            mMaskDataList.Clear();
            mMaskDataList.AddRange(datas);
           
        }
        /// <summary>
        /// 获取预约记录数据
        /// </summary>
        /// <returns></returns>
        public List<BookingData>  GetMaskData()
        {
            return mMaskDataList;
        }

        /// <summary>
        /// 获取已经签到数据（已完成）
        /// </summary>
        /// <returns></returns>
        public List<BookingData> GetSignedData()
        {
            List<BookingData> TmpList = new List<BookingData>();
            foreach (var item in mMaskDataList)
            {
                if (item.sessionStatus==2 || item.sessionStatus == 5)
                {
                    TmpList.Add(item);
                }
            }

            return TmpList;
        }

        /// <summary>
        /// 获取代签到数据
        /// </summary>
        /// <returns></returns>
        public List<BookingData> GetreservedData()
        {
            List<BookingData> TmpList = new List<BookingData>();
            foreach (var item in mMaskDataList)
            {
                if (item.sessionStatus == 1)
                {
                    TmpList.Add(item);
                }
            }

            return TmpList;
        }
    }

}
