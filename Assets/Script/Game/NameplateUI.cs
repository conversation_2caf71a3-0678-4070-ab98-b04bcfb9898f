using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace QFramework.ZSS
{
    public class NameplateUI : MonoBehaviour
    {
        [SerializeField] private Text nameText;
        [SerializeField] private Image avatarImage;

        private Transform _target;
        private Camera _mainCam;
        private bool _isVisible = true;
        private CanvasGroup _canvasGroup;

        [Header("Fade Settings")]
        public float fadeSpeed = 5f; // 淡入淡出速度
        private float _targetAlpha = 0f;
        void Awake()
        {
            _canvasGroup = GetComponent<CanvasGroup>();
            _canvasGroup.alpha = 0;
        }
        public void Initialize(Transform target, string displayName, string avatar)
        {
            _target = target;
            _mainCam = Camera.main;

            nameText.text = displayName;
            avatarImage.sprite = StartManager.Instance.GetMinAvaterSprite(avatar);
        }

        // 更新UI位置（在LateUpdate中调用）
        public void UpdatePosition(Vector3 offset)
        {
            if (_target == null) return;

            // 世界坐标转屏幕坐标
            Vector3 worldPos = _mainCam.WorldToScreenPoint( _target.position + offset);
            transform.position = worldPos;

           
        }


        void Update()
        {
            // 淡入淡出逻辑（保持不变）
            _canvasGroup.alpha = Mathf.MoveTowards(_canvasGroup.alpha, _targetAlpha, fadeSpeed * Time.deltaTime);
            if (_canvasGroup.alpha <= 0.01f && _targetAlpha == 0)
                gameObject.SetActive(false);
        }
        public void SetVisibility(bool isVisible)
        {
            _targetAlpha = isVisible ? 1f : 0f;
            if (isVisible) gameObject.SetActive(true);
        }


        // 检查可见性（如遮挡、距离等）
        public void CheckVisibility()
        {
            if (_target == null) return;

            // 示例：距离超过20米则隐藏
            float distance = Vector3.Distance(_mainCam.transform.position, _target.position);
            bool shouldShow = distance < 20f;

            if (_isVisible != shouldShow)
            {
                gameObject.SetActive(shouldShow);
                _isVisible = shouldShow;
            }
        }
    }
}

