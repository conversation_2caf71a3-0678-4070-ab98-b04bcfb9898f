using UnityEngine;
using QFramework;

// 1.请在菜单 编辑器扩展/Namespace Settings 里设置命名空间
// 2.命名空间更改后，生成代码之后，需要把逻辑代码文件（非 Designer）的命名空间手动更改
namespace QFramework.ZSS
{
	public partial class ClothingItem : ViewController,IController
	{
        private string ID;
        private CostumeData m_Data;


        private void Awake()
        {
            // 监听器现在在UpdateUI中动态设置，这里保持空即可
        }
        public void InIt(CostumeData data)
        {
            m_Data = data;
            UpdateUI();
        }

      

        public void UpdateUI()
        {
            // 安全检查：确保数据不为空
            if (m_Data == null)
            {
                Debug.LogError("ClothingItem: m_Data为空，无法更新UI");
                return;
            }
            
            // 安全检查：确保UI组件不为空
            if (Img_Icon == null)
            {
                Debug.LogError("ClothingItem: Img_Icon组件为空，无法设置图标");
                return;
            }
            
            // 基础信息设置
            ID = m_Data.assetId;
            Img_Icon.gameObject.SetActive(true);
            
            // 安全获取精灵图片 - 使用延迟获取机制
            var reloadingPanel = SyncUIKit.GetPanel<ReloadingPanel>();
            if (reloadingPanel != null)
            {
                var sprite = reloadingPanel.GetClothingSprite(ID);
                if (sprite != null)
                {
                    Img_Icon.sprite = sprite;
                }
                else
                {
                    Debug.LogWarning($"ClothingItem: 无法获取ID为{ID}的精灵图片");
                    Img_Icon.gameObject.SetActive(false);
                }
            }
            else
            {
                // ReloadingPanel可能还在初始化中，使用协程延迟重试
                Debug.LogWarning("ClothingItem: ReloadingPanel实例暂时不可用，将延迟重试");
                StartCoroutine(DelayedUpdateSprite());
            }

            // 安全检查：确保其他UI组件不为空
            if (Txt_Content == null)
            {
                Debug.LogError("ClothingItem: Txt_Content组件为空");
                return;
            }
            
            if (Toggle_OnClick == null)
            {
                Debug.LogError("ClothingItem: Toggle_OnClick组件为空");
                return;
            }
            
            // 颜色常量定义（避免重复创建Color）
            Color unlockedColor = new Color(0.05882353f, 0.5607843f, 0.4862745f, 1);
            Color lockedColor = Color.black;
            Color canActivateColor = new Color(0.9f, 0.6f, 0.1f, 1); // 橙色表示可激活

            // 安全获取用户积分信息
            var userModel = this.GetModel<UserModel>();
            if (userModel == null)
            {
                Debug.LogError("ClothingItem: UserModel为空，无法获取用户积分");
                return;
            }
            
            int userScore = userModel.GetUserScore();
            bool canActivate = userScore >= m_Data.requiredScore && m_Data.isActive == 0;

            // 状态判断逻辑
            if (m_Data.isDefault == 1)
            {
                SetTextStatus("已穿戴", unlockedColor);
            }
            else if (m_Data.isActive == 1)
            {
                SetTextStatus("已解锁", unlockedColor);
            }
            else if (canActivate)
            {
                SetTextStatus($"可激活 (需要{m_Data.requiredScore}积分)", canActivateColor);
            }
            else
            {
                int neededScore = m_Data.requiredScore - userScore;
                SetTextStatus($"积分不足 (还需{neededScore}积分)", lockedColor);
            }

            // 图标状态控制（安全检查）
            if (Img_Unlock != null)
                Img_Unlock.gameObject.SetActive(m_Data.type != 3 && m_Data.isActive == 1);
            else
                Debug.LogWarning("ClothingItem: Img_Unlock组件为空");
                
            if (Img_NoUnlock != null)
                Img_NoUnlock.gameObject.SetActive(m_Data.type != 3 && m_Data.isActive == 0);
            else
                Debug.LogWarning("ClothingItem: Img_NoUnlock组件为空");

            // Toggle交互控制 - 已激活的服装可以选择，或者可激活的服装可以激活
            Toggle_OnClick.interactable = m_Data.isActive == 1;
            
            // 如果可以激活，添加激活功能
            if (canActivate)
            {
                // 移除之前的监听器避免重复
                Toggle_OnClick.onValueChanged.RemoveAllListeners();
                
                // 添加激活监听器
                Toggle_OnClick.onValueChanged.AddListener((isOn) => {
                    if (isOn)
                    {
                        // 执行激活命令
                        var activateCommand = new ActivateCostumeCommand(m_Data.id, m_Data);
                        activateCommand.Execute();
                        
                        // 重置Toggle状态，因为激活是一次性操作
                        Toggle_OnClick.isOn = false;
                    }
                });
                
                Toggle_OnClick.interactable = true; // 可激活的服装应该可以点击
            }
            else if (m_Data.isActive == 1)
            {
                // 恢复正常的选择逻辑
                Toggle_OnClick.onValueChanged.RemoveAllListeners();
                Toggle_OnClick.onValueChanged.AddListener((e) => {
                    if (e)
                    {
                        //设置当前选择的CostumeData
                        this.GetSystem<ReloadSystem>().CurrentCostumeData = m_Data;
                        //更改模型
                        this.GetSystem<ReloadSystem>().ChangeReloadModel(m_Data);
                        //设置当前选择的CostumeData 加入到List中，使用正确的type作为key
                        this.GetSystem<ReloadSystem>().CurrentDic[m_Data.type] = m_Data;
                    }
                    else
                    {
                        // 取消选中时才移除
                        this.GetSystem<ReloadSystem>().CurrentDic.Remove(m_Data.type);
                    }
                    
                    // 安全设置Img_wear状态
                    if (Img_wear != null)
                        Img_wear.gameObject.SetActive(e);
                    else
                        Debug.LogWarning("ClothingItem: Img_wear组件为空");
                });
            }

            // 安全设置Img_wear初始状态
            if (Img_wear != null)
                Img_wear.gameObject.SetActive(false);
            else
                Debug.LogWarning("ClothingItem: Img_wear组件为空，无法设置初始状态");
        }

        /// <summary>
        /// 设置文本状态（提取的公共方法）
        /// </summary>
        private void SetTextStatus(string text, Color color)
        {
            Txt_Content.text = text;
            Txt_Content.color = color;
        }

        /// <summary>
        /// 延迟更新精灵图片的协程
        /// </summary>
        private System.Collections.IEnumerator DelayedUpdateSprite()
        {
            // 等待一帧，让ReloadingPanel有时间完成初始化
            yield return null;
            
            int retryCount = 0;
            const int maxRetries = 10; // 最大重试次数
            
            while (retryCount < maxRetries)
            {
                var reloadingPanel = SyncUIKit.GetPanel<ReloadingPanel>();
                if (reloadingPanel != null)
                {
                    var sprite = reloadingPanel.GetClothingSprite(ID);
                    if (sprite != null)
                    {
                        Img_Icon.sprite = sprite;
                        Debug.Log($"ClothingItem: 延迟重试成功获取ID为{ID}的精灵图片");
                    }
                    else
                    {
                        Debug.LogWarning($"ClothingItem: 无法获取ID为{ID}的精灵图片");
                        Img_Icon.gameObject.SetActive(false);
                    }
                    yield break; // 成功获取到面板，退出协程
                }
                
                retryCount++;
                yield return new UnityEngine.WaitForSeconds(0.1f); // 等待0.1秒后重试
            }
            
            // 重试失败，隐藏图标
            Debug.LogError($"ClothingItem: 经过{maxRetries}次重试仍无法获取ReloadingPanel实例");
            Img_Icon.gameObject.SetActive(false);
        }

        public IArchitecture GetArchitecture()
        {
            return LibraryApp.Interface;
        }
    }
}
