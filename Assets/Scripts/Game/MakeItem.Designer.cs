// Generate Id:cecf7949-e319-43b3-b4db-372eb99ff6cc
using UnityEngine;

// 1.请在菜单 编辑器扩展/Namespace Settings 里设置命名空间
// 2.命名空间更改后，生成代码之后，需要把逻辑代码文件（非 Designer）的命名空间手动更改
namespace QFramework.ZSS
{
	public partial class MakeItem : QFramework.IController
	{
		public UnityEngine.UI.Image Img_Min;
		
		public UnityEngine.UI.Image Img_Big;
		
		public UnityEngine.UI.Button Btn_Cancel;
		
		public UnityEngine.UI.Image Stay;
		
		public UnityEngine.UI.Image Cancel;
		
		public UnityEngine.UI.Image Succes;
		
		public UnityEngine.UI.Image Overdue;
		
		public UnityEngine.UI.Image Party_Avatar;
		
		public UnityEngine.UI.Image Round_Avatar;
		
		public UnityEngine.UI.Text Txt_Name;
		
		public UnityEngine.UI.Text Txt_Data;
		
		public UnityEngine.UI.Text Txt_Time;
		
		public UnityEngine.UI.Text Txt_SeatName;
		
		QFramework.IArchitecture QFramework.IBelongToArchitecture.GetArchitecture()=>QFramework.ZSS.LibraryApp.Interface;
	}
}
